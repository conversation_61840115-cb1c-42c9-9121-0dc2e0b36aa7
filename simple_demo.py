#!/usr/bin/env python3

import json
import sys
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any
from enum import Enum

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


class FunctionCategory(Enum):
    DATA_PROCESSING = "data_processing"
    FILE_OPERATIONS = "file_operations"
    COMMUNICATION = "communication"
    DATABASE = "database"
    WEB_SCRAPING = "web_scraping"
    ANALYTICS = "analytics"


@dataclass
class FunctionCall:
    function_name: str
    parameters: Dict[str, Any]
    confidence: float
    reasoning: str = ""


@dataclass
class ExecutionPlan:
    function_calls: List[FunctionCall]
    dependencies: Dict[str, List[str]]
    reasoning: str
    confidence: float


@dataclass
class PipelineResult:
    query: str
    execution_plan: ExecutionPlan
    success: bool
    error_message: str = None


class SimplePipeline:

    def __init__(self):
        self.functions = self._get_available_functions()

    def _get_available_functions(self):
        return {
            "filter_data": {"category": "data_processing", "description": "Filter data based on criteria"},
            "sort_data": {"category": "data_processing", "description": "Sort data by field"},
            "aggregate_data": {"category": "data_processing", "description": "Aggregate data using operation"},
            "group_data": {"category": "data_processing", "description": "Group data by field"},
            "merge_datasets": {"category": "data_processing", "description": "Merge two datasets"},
            "read_file": {"category": "file_operations", "description": "Read content from file"},
            "write_file": {"category": "file_operations", "description": "Write content to file"},
            "copy_file": {"category": "file_operations", "description": "Copy file"},
            "delete_file": {"category": "file_operations", "description": "Delete file"},
            "list_files": {"category": "file_operations", "description": "List files in directory"},
            "send_email": {"category": "communication", "description": "Send email message"},
            "send_sms": {"category": "communication", "description": "Send SMS message"},
            "make_api_call": {"category": "communication", "description": "Make HTTP API call"},
            "post_to_slack": {"category": "communication", "description": "Post to Slack channel"},
            "execute_query": {"category": "database", "description": "Execute SQL query"},
            "insert_record": {"category": "database", "description": "Insert database record"},
            "update_record": {"category": "database", "description": "Update database record"},
            "backup_database": {"category": "database", "description": "Create database backup"},
            "scrape_webpage": {"category": "web_scraping", "description": "Extract data from webpage"},
            "download_file": {"category": "web_scraping", "description": "Download file from URL"},
            "monitor_webpage": {"category": "web_scraping", "description": "Monitor webpage for changes"},
            "calculate_statistics": {"category": "analytics", "description": "Calculate statistical measures"},
            "generate_report": {"category": "analytics", "description": "Generate analytical report"},
            "create_visualization": {"category": "analytics", "description": "Create data visualization"},
            "predict_values": {"category": "analytics", "description": "Make predictions using ML model"},
            "add_numbers": {"category": "math", "description": "Add two or more numbers"},
            "subtract_numbers": {"category": "math", "description": "Subtract numbers"},
            "multiply_numbers": {"category": "math", "description": "Multiply numbers"},
            "divide_numbers": {"category": "math", "description": "Divide numbers"},
            "calculate_expression": {"category": "math", "description": "Calculate mathematical expression"},
        }
    
    def process_query(self, query: str) -> PipelineResult:
        try:
            function_calls = self._extract_functions_heuristic(query)
            dependencies = self._build_dependencies(function_calls)

            execution_plan = ExecutionPlan(
                function_calls=function_calls,
                dependencies=dependencies,
                reasoning=f"Heuristic-based plan for query: {query}",
                confidence=0.8
            )

            return PipelineResult(
                query=query,
                execution_plan=execution_plan,
                success=True
            )

        except Exception as e:
            return PipelineResult(
                query=query,
                execution_plan=ExecutionPlan([], {}, "", 0.0),
                success=False,
                error_message=str(e)
            )
    
    def _extract_functions_heuristic(self, query: str) -> List[FunctionCall]:
        query_lower = query.lower()
        function_calls = []

        if any(word in query_lower for word in ["get", "retrieve", "fetch", "find", "show", "list"]):
            if any(word in query_lower for word in ["invoice", "data", "record", "customer", "user"]):
                sql_query, table_name = self._generate_smart_sql(query_lower)

                function_calls.append(FunctionCall(
                    function_name="execute_query",
                    parameters={
                        "query": sql_query,
                        "database": "main_db",
                        "table": table_name,
                        "original_query": query
                    },
                    confidence=0.9,
                    reasoning=f"Retrieve {table_name} data with intelligent SQL generation"
                ))
        
        if any(word in query_lower for word in ["summarize", "total", "aggregate"]) and not any(word in query_lower for word in ["number", "numbers"]):
            function_calls.append(FunctionCall(
                function_name="aggregate_data",
                parameters={"data": "$execute_query", "operation": "sum", "field": "amount"},
                confidence=0.85,
                reasoning="Calculate totals or summaries"
            ))

        if any(word in query_lower for word in ["filter", "where", "only", "active"]):
            function_calls.append(FunctionCall(
                function_name="filter_data",
                parameters={"data": "$execute_query", "criteria": {"status": "active"}},
                confidence=0.8,
                reasoning="Filter data based on criteria"
            ))

        if any(word in query_lower for word in ["email", "mail"]) and "send" in query_lower:
            function_calls.append(FunctionCall(
                function_name="send_email",
                parameters={
                    "to": "<EMAIL>",
                    "subject": "Report Summary",
                    "body": "$aggregate_data"
                },
                confidence=0.75,
                reasoning="Send results via email"
            ))

        if any(word in query_lower for word in ["sms", "text", "message"]) and "send" in query_lower:
            import re
            phone_pattern = r'\b\d{10,15}\b'
            phone_matches = re.findall(phone_pattern, query)
            phone_number = phone_matches[0] if phone_matches else "1234567890"

            message_content = "good morning"
            if "with message" in query_lower:
                message_start = query_lower.find("with message") + len("with message")
                message_content = query[message_start:].strip()
            elif "message" in query_lower:
                words = query_lower.split()
                if "message" in words:
                    message_idx = words.index("message")
                    if message_idx + 1 < len(words):
                        message_content = " ".join(query.split()[message_idx + 1:])

            function_calls.append(FunctionCall(
                function_name="send_sms",
                parameters={
                    "to": phone_number,
                    "message": message_content
                },
                confidence=0.85,
                reasoning=f"Send SMS to {phone_number} with message: {message_content}"
            ))

        if any(word in query_lower for word in ["report", "generate", "create", "analysis"]):
            function_calls.append(FunctionCall(
                function_name="generate_report",
                parameters={"data": "$aggregate_data", "template": "summary_report", "format": "pdf"},
                confidence=0.8,
                reasoning="Generate comprehensive report"
            ))

        if any(word in query_lower for word in ["chart", "graph", "visualization", "plot"]):
            function_calls.append(FunctionCall(
                function_name="create_visualization",
                parameters={"data": "$aggregate_data", "chart_type": "bar", "title": "Data Summary"},
                confidence=0.75,
                reasoning="Create visual representation"
            ))

        if any(word in query_lower for word in ["scrape", "monitor", "website", "competitor"]):
            function_calls.append(FunctionCall(
                function_name="scrape_webpage",
                parameters={"url": "https://example.com", "selectors": {"price": ".price"}},
                confidence=0.7,
                reasoning="Extract data from website"
            ))

        if any(word in query_lower for word in ["backup", "save", "archive"]):
            function_calls.append(FunctionCall(
                function_name="backup_database",
                parameters={"database": "main_db", "backup_path": "/backups/"},
                confidence=0.8,
                reasoning="Create data backup"
            ))

        if any(word in query_lower for word in ["add", "sum", "plus", "calculate sum", "addition"]):
            import re
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', query)
            if len(numbers) >= 2:
                function_calls.append(FunctionCall(
                    function_name="add_numbers",
                    parameters={"numbers": [float(n) for n in numbers]},
                    confidence=0.95,
                    reasoning=f"Add numbers: {', '.join(numbers)}"
                ))

        elif any(word in query_lower for word in ["subtract", "minus", "difference"]):
            import re
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', query)
            if len(numbers) >= 2:
                function_calls.append(FunctionCall(
                    function_name="subtract_numbers",
                    parameters={"numbers": [float(n) for n in numbers]},
                    confidence=0.95,
                    reasoning=f"Subtract numbers: {', '.join(numbers)}"
                ))

        elif any(word in query_lower for word in ["multiply", "times", "product"]):
            import re
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', query)
            if len(numbers) >= 2:
                function_calls.append(FunctionCall(
                    function_name="multiply_numbers",
                    parameters={"numbers": [float(n) for n in numbers]},
                    confidence=0.95,
                    reasoning=f"Multiply numbers: {', '.join(numbers)}"
                ))

        elif any(word in query_lower for word in ["divide", "division"]):
            import re
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', query)
            if len(numbers) >= 2:
                function_calls.append(FunctionCall(
                    function_name="divide_numbers",
                    parameters={"numbers": [float(n) for n in numbers]},
                    confidence=0.95,
                    reasoning=f"Divide numbers: {', '.join(numbers)}"
                ))

        return function_calls

    def _generate_smart_sql(self, query_lower: str) -> tuple:
        import re

        table_name = "data"
        if "invoice" in query_lower:
            table_name = "invoices"
        elif "customer" in query_lower:
            table_name = "customers"
        elif "user" in query_lower:
            table_name = "users"
        elif "order" in query_lower:
            table_name = "orders"
        elif "transaction" in query_lower:
            table_name = "transactions"

        conditions = []

        months = {
            "january": "01", "jan": "01",
            "february": "02", "feb": "02",
            "march": "03", "mar": "03",
            "april": "04", "apr": "04",
            "may": "05",
            "june": "06", "jun": "06",
            "july": "07", "jul": "07",
            "august": "08", "aug": "08",
            "september": "09", "sep": "09", "sept": "09",
            "october": "10", "oct": "10",
            "november": "11", "nov": "11",
            "december": "12", "dec": "12"
        }

        for month_name, month_num in months.items():
            if month_name in query_lower:
                date_col = "date"
                if table_name == "invoices":
                    date_col = "invoice_date"
                elif table_name == "orders":
                    date_col = "order_date"
                elif table_name == "transactions":
                    date_col = "transaction_date"

                conditions.append(f"MONTH({date_col}) = {month_num}")
                break

        year_match = re.search(r'\b(20\d{2})\b', query_lower)
        if year_match:
            year = year_match.group(1)
            date_col = "date"
            if table_name == "invoices":
                date_col = "invoice_date"
            elif table_name == "orders":
                date_col = "order_date"
            elif table_name == "transactions":
                date_col = "transaction_date"
            conditions.append(f"YEAR({date_col}) = {year}")

        if "active" in query_lower:
            conditions.append("status = 'active'")
        elif "pending" in query_lower:
            conditions.append("status = 'pending'")
        elif "completed" in query_lower:
            conditions.append("status = 'completed'")

        amount_match = re.search(r'over\s+\$?(\d+)', query_lower)
        if amount_match:
            amount = amount_match.group(1)
            conditions.append(f"amount > {amount}")

        amount_match = re.search(r'above\s+\$?(\d+)', query_lower)
        if amount_match:
            amount = amount_match.group(1)
            conditions.append(f"amount > {amount}")

        if conditions:
            where_clause = " AND ".join(conditions)
            sql_query = f"SELECT * FROM {table_name} WHERE {where_clause}"
        else:
            sql_query = f"SELECT * FROM {table_name}"

        return sql_query, table_name

    def _build_dependencies(self, function_calls: List[FunctionCall]) -> Dict[str, List[str]]:
        dependencies = {}

        for call in function_calls:
            deps = []

            if call.function_name in ["aggregate_data", "filter_data", "generate_report"]:
                if any(fc.function_name == "execute_query" for fc in function_calls):
                    deps.append("execute_query")

            if call.function_name in ["send_email", "create_visualization"]:
                if any(fc.function_name == "aggregate_data" for fc in function_calls):
                    deps.append("aggregate_data")
                elif any(fc.function_name == "filter_data" for fc in function_calls):
                    deps.append("filter_data")

            if call.function_name in ["update_record"]:
                if any(fc.function_name == "scrape_webpage" for fc in function_calls):
                    deps.append("scrape_webpage")

            if deps:
                dependencies[call.function_name] = deps

        return dependencies
    
    def get_available_functions(self) -> List[str]:
        return list(self.functions.keys())

    def search_functions(self, term: str) -> List[str]:
        term_lower = term.lower()
        results = []

        for func_name, func_info in self.functions.items():
            if (term_lower in func_name.lower() or
                term_lower in func_info["description"].lower() or
                term_lower in func_info["category"]):
                results.append(func_name)

        return results

    def execute_function(self, function_call: FunctionCall) -> Dict[str, Any]:
        try:
            if function_call.function_name == "send_sms":
                from functions.sms_implementation import execute_send_sms
                return execute_send_sms(function_call.parameters)

            elif function_call.function_name == "execute_query":
                return self._execute_database_query(function_call.parameters)

            elif function_call.function_name == "send_email":
                return {
                    "success": True,
                    "result": f"Email sent to {function_call.parameters.get('to', 'unknown')}",
                    "error": None
                }

            elif function_call.function_name == "add_numbers":
                # Addition implementation
                numbers = function_call.parameters.get("numbers", [])
                if len(numbers) < 2:
                    return {"success": False, "error": "Need at least 2 numbers to add", "result": None}

                result = sum(numbers)
                print(f"CALCULATION:")
                print(f"   Operation: Addition")
                print(f"   Numbers: {' + '.join(map(str, numbers))}")
                print(f"   Result: {result}")

                return {
                    "success": True,
                    "result": result,
                    "error": None,
                    "calculation": f"{' + '.join(map(str, numbers))} = {result}"
                }

            elif function_call.function_name == "subtract_numbers":
                numbers = function_call.parameters.get("numbers", [])
                if len(numbers) < 2:
                    return {"success": False, "error": "Need at least 2 numbers to subtract", "result": None}

                result = numbers[0]
                for num in numbers[1:]:
                    result -= num

                print(f"CALCULATION:")
                print(f"   Operation: Subtraction")
                print(f"   Numbers: {' - '.join(map(str, numbers))}")
                print(f"   Result: {result}")

                return {
                    "success": True,
                    "result": result,
                    "error": None,
                    "calculation": f"{' - '.join(map(str, numbers))} = {result}"
                }

            elif function_call.function_name == "multiply_numbers":
                numbers = function_call.parameters.get("numbers", [])
                if len(numbers) < 2:
                    return {"success": False, "error": "Need at least 2 numbers to multiply", "result": None}

                result = 1
                for num in numbers:
                    result *= num

                print(f"CALCULATION:")
                print(f"   Operation: Multiplication")
                print(f"   Numbers: {' × '.join(map(str, numbers))}")
                print(f"   Result: {result}")

                return {
                    "success": True,
                    "result": result,
                    "error": None,
                    "calculation": f"{' × '.join(map(str, numbers))} = {result}"
                }

            elif function_call.function_name == "divide_numbers":
                numbers = function_call.parameters.get("numbers", [])
                if len(numbers) < 2:
                    return {"success": False, "error": "Need at least 2 numbers to divide", "result": None}

                if any(num == 0 for num in numbers[1:]):
                    return {"success": False, "error": "Cannot divide by zero", "result": None}

                result = numbers[0]
                for num in numbers[1:]:
                    result /= num

                print(f"CALCULATION:")
                print(f"   Operation: Division")
                print(f"   Numbers: {' ÷ '.join(map(str, numbers))}")
                print(f"   Result: {result}")

                return {
                    "success": True,
                    "result": result,
                    "error": None,
                    "calculation": f"{' ÷ '.join(map(str, numbers))} = {result}"
                }

            else:
                # Mock implementation for other functions
                return {
                    "success": True,
                    "result": f"Function {function_call.function_name} executed with parameters: {function_call.parameters}",
                    "error": None
                }

        except Exception as e:
            return {
                "success": False,
                "result": None,
                "error": str(e)
            }

    def _execute_database_query(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        try:
            query = parameters.get("query", "")
            table = parameters.get("table", "data")
            original_query = parameters.get("original_query", "")

            print(f"DATABASE QUERY:")
            print(f"   SQL: {query}")
            print(f"   Table: {table}")

            if table == "invoices":
                mock_data = self._generate_mock_invoices(query)
            elif table == "customers":
                mock_data = self._generate_mock_customers(query)
            elif table == "orders":
                mock_data = self._generate_mock_orders(query)
            elif table == "transactions":
                mock_data = self._generate_mock_transactions(query)
            else:
                mock_data = [{"id": 1, "data": "sample data"}]

            print(f"   Found {len(mock_data)} records")

            if mock_data:
                print(f"   Sample Results:")
                for i, record in enumerate(mock_data[:3], 1):
                    print(f"      {i}. {record}")
                if len(mock_data) > 3:
                    print(f"      ... and {len(mock_data) - 3} more records")

            return {
                "success": True,
                "result": mock_data,
                "error": None,
                "query": query,
                "table": table,
                "count": len(mock_data)
            }

        except Exception as e:
            return {
                "success": False,
                "result": None,
                "error": str(e)
            }

    def _generate_mock_invoices(self, query: str) -> List[Dict[str, Any]]:
        import random
        from datetime import datetime, timedelta

        is_march = "MONTH(invoice_date) = 03" in query or "march" in query.lower()

        invoices = []
        base_date = datetime(2024, 3, 1) if is_march else datetime(2024, 1, 1)

        for i in range(1, 16):
            if is_march:
                invoice_date = base_date + timedelta(days=random.randint(0, 30))
            else:
                invoice_date = base_date + timedelta(days=random.randint(0, 365))

            invoice = {
                "invoice_id": f"INV-{2024}-{i:04d}",
                "customer_id": f"CUST-{random.randint(1000, 9999)}",
                "customer_name": random.choice([
                    "Acme Corp", "Tech Solutions Inc", "Global Industries",
                    "Innovation Labs", "Future Systems", "Digital Dynamics"
                ]),
                "invoice_date": invoice_date.strftime("%Y-%m-%d"),
                "amount": round(random.uniform(500, 5000), 2),
                "status": random.choice(["paid", "pending", "overdue"]),
                "description": random.choice([
                    "Software License", "Consulting Services", "Hardware Purchase",
                    "Maintenance Contract", "Training Services", "Support Package"
                ])
            }
            invoices.append(invoice)

        return invoices

    def _generate_mock_customers(self, query: str) -> List[Dict[str, Any]]:
        customers = [
            {"customer_id": "CUST-1001", "name": "Acme Corp", "status": "active", "email": "<EMAIL>"},
            {"customer_id": "CUST-1002", "name": "Tech Solutions Inc", "status": "active", "email": "<EMAIL>"},
            {"customer_id": "CUST-1003", "name": "Global Industries", "status": "pending", "email": "<EMAIL>"},
            {"customer_id": "CUST-1004", "name": "Innovation Labs", "status": "active", "email": "<EMAIL>"},
            {"customer_id": "CUST-1005", "name": "Future Systems", "status": "inactive", "email": "<EMAIL>"}
        ]

        if "status = 'active'" in query:
            customers = [c for c in customers if c["status"] == "active"]

        return customers

    def _generate_mock_orders(self, query: str) -> List[Dict[str, Any]]:
        """Generate mock order data."""
        import random
        from datetime import datetime, timedelta

        orders = []
        for i in range(1, 11):
            order_date = datetime(2024, 3, random.randint(1, 31))
            order = {
                "order_id": f"ORD-{i:04d}",
                "customer_id": f"CUST-{random.randint(1001, 1005)}",
                "order_date": order_date.strftime("%Y-%m-%d"),
                "amount": round(random.uniform(100, 2000), 2),
                "status": random.choice(["completed", "pending", "shipped"])
            }
            orders.append(order)

        return orders

    def _generate_mock_transactions(self, query: str) -> List[Dict[str, Any]]:
        """Generate mock transaction data."""
        import random
        from datetime import datetime, timedelta

        transactions = []
        for i in range(1, 21):
            transaction_date = datetime(2024, 3, random.randint(1, 31))
            amount = round(random.uniform(50, 3000), 2)

            transaction = {
                "transaction_id": f"TXN-{i:06d}",
                "transaction_date": transaction_date.strftime("%Y-%m-%d"),
                "amount": amount,
                "type": random.choice(["payment", "refund", "charge"]),
                "status": "completed"
            }
            transactions.append(transaction)

        return transactions


def main():
    print("AI Function Call Pipeline - Simple Demo")
    print("=" * 50)

    pipeline = SimplePipeline()

    print(f"Available Functions: {len(pipeline.get_available_functions())}")

    example_queries = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a report.",
        "Search for transactions over $1000, analyze the patterns, and create a visualization.",
        "Scrape competitor prices, update our database, and send alerts.",
        "Backup the database and notify the admin team."
    ]

    print("\nProcessing Example Queries:")
    print("-" * 40)

    for i, query in enumerate(example_queries, 1):
        print(f"\n{i}. Query: {query}")

        result = pipeline.process_query(query)

        if result.success:
            plan = result.execution_plan
            print(f"Success! (Confidence: {plan.confidence:.2f})")
            print(f"Functions: {len(plan.function_calls)}")

            if plan.function_calls:
                print("\nExecution Plan:")
                for j, call in enumerate(plan.function_calls, 1):
                    print(f"   {j}. {call.function_name}")
                    print(f"      Parameters: {call.parameters}")
                    print(f"      Reasoning: {call.reasoning}")

                if plan.dependencies:
                    print("\nDependencies:")
                    for func, deps in plan.dependencies.items():
                        if deps:
                            print(f"   {func} ← {', '.join(deps)}")
        else:
            print(f"Failed: {result.error_message}")

        print("-" * 40)


    print("\nFunction Search Demo:")
    search_terms = ["email", "data", "report", "backup"]
 
    for term in search_terms:
        results = pipeline.search_functions(term)
        print(f"'{term}': {results}")
    
    print("\n✨ Simple Demo Complete!")
    print("This demonstrates the core concept of converting natural language")
    print("queries into structured function call sequences with dependencies.")


if __name__ == "__main__":
    main()
