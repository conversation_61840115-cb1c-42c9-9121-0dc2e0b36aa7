#!/usr/bin/env python3
"""
Detailed Output Demo - Shows the exact type of output requested for query analysis.
"""

from simple_demo import <PERSON><PERSON><PERSON><PERSON><PERSON>


def analyze_query_with_detailed_output(pipeline, query):
    """Analyze a query and provide detailed output showing identified functions."""
    print(f"📋 Query Analysis:")
    print(f"Query: \"{query}\"")
    
    # Process the query
    result = pipeline.process_query(query)
    
    if result.success:
        print(f"\nThe pipeline identified the need for:")
        
        # Show identified functions with clear descriptions
        for i, call in enumerate(result.execution_plan.function_calls, 1):
            # Create descriptive purpose based on function name and context
            if "execute_query" in call.function_name or "get" in call.function_name.lower():
                if "invoice" in query.lower():
                    purpose = "retrieve invoices"
                elif "customer" in query.lower():
                    purpose = "retrieve customer data"
                elif "user" in query.lower():
                    purpose = "retrieve user data"
                elif "sales" in query.lower():
                    purpose = "retrieve sales data"
                else:
                    purpose = "retrieve data"
            
            elif "aggregate" in call.function_name or "sum" in call.function_name or "calculate" in call.function_name:
                if "total" in query.lower() or "sum" in query.lower():
                    purpose = "summarize the total amount"
                elif "average" in query.lower() or "avg" in query.lower():
                    purpose = "calculate averages"
                elif "count" in query.lower():
                    purpose = "count records"
                else:
                    purpose = "summarize the data"
            
            elif "send_email" in call.function_name or "email" in call.function_name:
                if "summary" in query.lower():
                    purpose = "send the summary via email"
                elif "report" in query.lower():
                    purpose = "send the report via email"
                elif "notification" in query.lower():
                    purpose = "send notification email"
                else:
                    purpose = "send the email"
            
            elif "filter" in call.function_name:
                purpose = "filter the data"
            
            elif "generate_report" in call.function_name or "report" in call.function_name:
                purpose = "generate a report"
            
            elif "scrape" in call.function_name:
                purpose = "extract web data"
            
            elif "backup" in call.function_name:
                purpose = "backup the data"
            
            elif "create_visualization" in call.function_name or "chart" in call.function_name:
                purpose = "create visualizations"
            
            elif "update" in call.function_name:
                purpose = "update records"
            
            elif "schedule" in call.function_name:
                purpose = "schedule tasks"
            
            else:
                purpose = call.reasoning.lower()
            
            print(f"  - A function to {purpose}.")
        
        print(f"\n📋 Execution Plan:")
        for i, call in enumerate(result.execution_plan.function_calls, 1):
            print(f"  {i}. {call.function_name}")
            print(f"     Purpose: {call.reasoning}")
            print(f"     Parameters: {call.parameters}")
        
        if result.execution_plan.dependencies:
            print(f"\n🔗 Dependencies:")
            for func, deps in result.execution_plan.dependencies.items():
                if deps:
                    print(f"  {func} depends on: {', '.join(deps)}")
        
        print(f"\n📈 Confidence: {result.execution_plan.confidence:.2f}")
        print(f"💡 Strategy: {result.execution_plan.reasoning}")
    
    else:
        print(f"❌ Failed to process query: {result.error_message}")
    
    print("=" * 60)


def main():
    """Run detailed output demonstrations."""
    print("🚀 AI Function Call Pipeline - Detailed Output Demo")
    print("=" * 60)
    
    # Create pipeline
    pipeline = SimplePipeline()
    
    # Example queries with expected detailed output
    example_queries = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a comprehensive report.",
        "Extract sales data from the database, calculate monthly totals, and create visualizations.",
        "Scrape competitor pricing information, update our database, and send alerts to the team.",
        "Backup all customer records, verify the backup integrity, and notify the admin team.",
        "Analyze user behavior patterns, generate insights, and schedule weekly delivery to stakeholders."
    ]
    
    for i, query in enumerate(example_queries, 1):
        print(f"\n🔍 Example {i}:")
        analyze_query_with_detailed_output(pipeline, query)
    
    # Interactive mode
    print("\n✨ Interactive Mode - Enter your own query:")
    try:
        while True:
            user_query = input("\nEnter your query (or 'quit' to exit): ").strip()
            
            if user_query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_query:
                print()
                analyze_query_with_detailed_output(pipeline, user_query)
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    main()
