# AI Function Call Pipeline

A sophisticated pipeline that leverages open-source AI models to process natural language queries and generate structured sequences of function calls.

## Overview

This project creates an intelligent pipeline that:
- Interprets natural language user queries
- Breaks down complex requests into discrete tasks
- Selects appropriate functions from a comprehensive library
- Returns logically structured execution flows with proper input/output mapping

## Features

- **Open-Source AI Integration**: Uses models like Mistral-7B or similar for query processing
- **Comprehensive Function Library**: ~50 predefined functions covering various domains
- **Intelligent Query Parsing**: Advanced natural language understanding
- **Execution Flow Generation**: Automatic sequencing with dependency management
- **Extensible Architecture**: Easy to add new functions and capabilities

## Project Structure

```
├── src/
│   ├── pipeline/           # Core pipeline implementation
│   ├── functions/          # Function library definitions
│   ├── models/            # AI model integration
│   ├── parsers/           # Query parsing components
│   └── utils/             # Utility functions
├── examples/              # Example queries and usage
├── tests/                 # Test suite
├── config/               # Configuration files
└── docs/                 # Documentation
```

## Quick Start

1. Install dependencies: `pip install -r requirements.txt`
2. Configure your model preferences in `config/model_config.yaml`
3. Run example: `python examples/basic_example.py`

## Example Usage

```python
from src.pipeline import FunctionCallPipeline

pipeline = FunctionCallPipeline()
query = "Retrieve all invoices for March, summarize the total amount, and send the summary to my email."

result = pipeline.process_query(query)
print(result.execution_plan)
```

## License

MIT License
