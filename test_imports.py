
"""
Test Imports - Shows how to properly import and use the pipeline modules.
"""

import sys
from pathlib import Path

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_direct_imports():
    """Test importing modules directly."""
    print(" Testing Direct Module Imports")
    print("=" * 40)
    
    try:
        
        print("📚 Testing Function Registry...")
        from functions.function_registry import FunctionRegistry
        registry = FunctionRegistry()
        functions = registry.get_all_functions()
        print(f"✅ Function Registry: {len(functions)} functions loaded")
        
        
        print("🔍 Testing Query Parser...")
        from parsers.query_parser import QueryParser
        parser = QueryParser()
        parsed = parser.parse("Test query")
        print(f"✅ Query Parser: Parsed query with confidence {parsed.confidence:.2f}")
        
        
        print("🤖 Testing Model Interface...")
        from models.model_interface import ModelConfig, DEFAULT_MODELS
        config = ModelConfig(model_name="test/model")
        print(f"✅ Model Interface: Created config for {config.model_name}")
        print(f"   Available default models: {list(DEFAULT_MODELS.keys())}")
        
        print("\n🎉 All direct imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_simple_pipeline():
    """Test the simple pipeline implementation."""
    print("\n🚀 Testing Simple Pipeline Implementation")
    print("=" * 45)
    
    try:
        
        from simple_demo import SimplePipeline, FunctionCall, ExecutionPlan
        
        
        pipeline = SimplePipeline()
        print(f"✅ Pipeline created with {len(pipeline.get_available_functions())} functions")
        
        query = "Get customer data and send email report"
        result = pipeline.process_query(query)
        
        print(f"✅ Query processed successfully:")
        print(f"   Query: {query}")
        print(f"   Success: {result.success}")
        print(f"   Functions: {len(result.execution_plan.function_calls)}")
        print(f"   Confidence: {result.execution_plan.confidence:.2f}")
        
        if result.execution_plan.function_calls:
            print("\n📋 Execution Plan:")
            for i, call in enumerate(result.execution_plan.function_calls, 1):
                print(f"   {i}. {call.function_name}")
                print(f"      Parameters: {call.parameters}")
                print(f"      Reasoning: {call.reasoning}")
        
        print("\n🎉 Simple pipeline test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Simple pipeline test failed: {e}")
        return False


def test_function_search():
    """Test function search capabilities."""
    print("\n🔍 Testing Function Search")
    print("=" * 30)
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        search_terms = ["email", "data", "report", "backup", "scrape"]
        
        for term in search_terms:
            results = pipeline.search_functions(term)
            print(f"'{term}': {len(results)} functions found")
            if results:
                print(f"   Examples: {', '.join(results[:3])}")
        
        print("\n✅ Function search test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Function search test failed: {e}")
        return False


def demonstrate_usage():
    """Demonstrate proper usage patterns."""
    print("\n💡 Usage Demonstration")
    print("=" * 25)
    
    print("""
🐍 Python Script Usage:
    
    # Import the working pipeline
    from simple_demo import SimplePipeline
    
    # Create pipeline instance
    pipeline = SimplePipeline()
    
    # Process a query
    result = pipeline.process_query("Your natural language query here")
    
    # Check results
    if result.success:
        for call in result.execution_plan.function_calls:
            print(f"{call.function_name}: {call.parameters}")

💻 Command Line Usage:
    
    # Process single query
    python main_working.py "Your query here"
    
    # Interactive mode
    python main_working.py --interactive
    
    # List functions
    python main_working.py --list-functions
    
    # Search functions
    python main_working.py --search-functions "email"

🧪 Testing:
    
    # Run test suite
    python run_tests.py
    
    # Run demos
    python simple_demo.py
    python demo.py

📚 Available Functions:
    
    Data Processing: filter_data, sort_data, aggregate_data, group_data, merge_datasets
    File Operations: read_file, write_file, copy_file, delete_file, list_files
    Communication: send_email, send_sms, make_api_call, post_to_slack
    Database: execute_query, insert_record, update_record, backup_database
    Web Scraping: scrape_webpage, download_file, monitor_webpage
    Analytics: calculate_statistics, generate_report, create_visualization, predict_values
    """)


def main():
    """Run all tests and demonstrations."""
    print("🚀 AI Function Call Pipeline - Import and Usage Test")
    print("=" * 55)
    
    success_count = 0
    total_tests = 3
    
    # Run tests
    if test_direct_imports():
        success_count += 1
    
    if test_simple_pipeline():
        success_count += 1
    
    if test_function_search():
        success_count += 1
    
    # Show usage patterns
    demonstrate_usage()
    
    # Summary
    print(f"\n📊 Test Summary: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! The pipeline is working correctly.")
        print("\n✨ You can now use the pipeline with:")
        print("   python main_working.py 'Your query here'")
        print("   python main_working.py --interactive")
    else:
        print(f"⚠️  {total_tests - success_count} tests failed")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
