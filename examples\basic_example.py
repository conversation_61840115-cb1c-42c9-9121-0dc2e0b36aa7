#!/usr/bin/env python3
"""
Basic Example - Demonstrates the AI Function Call Pipeline.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pipeline.core import FunctionCallPipeline, PipelineBuilder
from models.model_interface import ModelConfig, DEFAULT_MODELS
from utils.config_loader import ConfigLoader, setup_logging
from utils.execution_engine import MockExecutionEngine


def main():
    """Run basic pipeline examples."""
    print("🚀 AI Function Call Pipeline - Basic Example")
    print("=" * 50)
    
    # Setup logging
    config_loader = ConfigLoader()
    setup_logging(config_loader)
    
    # Example queries to test
    example_queries = [
        "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
        "Get customer data, filter by active status, and generate a report.",
        "Search for transactions over $1000, analyze the patterns, and create a visualization.",
        "Read the sales data file, calculate monthly totals, and backup the results.",
        "Find all users registered this week and send them a welcome email.",
    ]
    
    print("\n📋 Example Queries:")
    for i, query in enumerate(example_queries, 1):
        print(f"{i}. {query}")
    
    # Create pipeline with mock execution for safety
    print("\n🔧 Setting up pipeline...")
    
    try:
        # Option 1: Use builder pattern
        pipeline = (PipelineBuilder()
                   .with_model(DEFAULT_MODELS["mistral-7b"])
                   .with_execution(False)  # Disable actual execution for safety
                   .build())
        
        print("✅ Pipeline created successfully!")
        print(f"📊 Available functions: {len(pipeline.get_available_functions())}")
        
    except Exception as e:
        print(f"❌ Failed to create pipeline: {e}")
        print("💡 This might be because the model is not available.")
        print("   You can still see the pipeline structure and function definitions.")
        
        # Create a minimal pipeline for demonstration
        pipeline = create_demo_pipeline()
    
    # Process each example query
    print("\n🔍 Processing Example Queries:")
    print("-" * 40)
    
    for i, query in enumerate(example_queries, 1):
        print(f"\n{i}. Query: {query}")
        
        try:
            # Process the query
            result = pipeline.process_query(query)
            
            if result.success:
                print("✅ Processing successful!")
                print(f"📈 Confidence: {result.execution_plan.confidence:.2f}")
                print(f"🔧 Functions identified: {len(result.execution_plan.function_calls)}")
                
                # Show the execution plan
                if result.execution_plan.function_calls:
                    print("\n📋 Execution Plan:")
                    for j, call in enumerate(result.execution_plan.function_calls, 1):
                        print(f"   {j}. {call.function_name}({call.parameters})")
                        if call.reasoning:
                            print(f"      💭 {call.reasoning}")
                
                # Show dependencies if any
                if result.execution_plan.dependencies:
                    print("\n🔗 Dependencies:")
                    for func, deps in result.execution_plan.dependencies.items():
                        if deps:
                            print(f"   {func} depends on: {', '.join(deps)}")
                
                # Show reasoning
                if result.execution_plan.reasoning:
                    print(f"\n💡 Reasoning: {result.execution_plan.reasoning}")
                
            else:
                print(f"❌ Processing failed: {result.error_message}")
                
        except Exception as e:
            print(f"❌ Error processing query: {e}")
        
        print("-" * 40)
    
    # Demonstrate function search
    print("\n🔍 Function Search Examples:")
    search_terms = ["email", "data", "file", "report"]
    
    for term in search_terms:
        functions = pipeline.search_functions(term)
        print(f"'{term}': {len(functions)} functions found")
        if functions:
            print(f"   Examples: {', '.join(functions[:3])}")
    
    print("\n✨ Example completed!")
    print("💡 To enable actual function execution, set enable_execution=True in the config.")


def create_demo_pipeline():
    """Create a demo pipeline for when models are not available."""
    from functions.function_registry import FunctionRegistry
    from parsers.query_parser import QueryParser
    
    class DemoPipeline:
        def __init__(self):
            self.function_registry = FunctionRegistry()
            self.query_parser = QueryParser()
        
        def process_query(self, query):
            from models.model_interface import ExecutionPlan, FunctionCall
            from pipeline.core import PipelineResult
            
            # Parse the query
            parsed = self.query_parser.parse(query)
            
            # Create a simple demo execution plan
            function_calls = []
            
            # Simple heuristics for demo
            if "retrieve" in query.lower() or "get" in query.lower():
                function_calls.append(FunctionCall(
                    function_name="execute_query",
                    parameters={"query": "SELECT * FROM invoices WHERE month = 'March'"},
                    confidence=0.8,
                    reasoning="Retrieve data based on query"
                ))
            
            if "summarize" in query.lower() or "total" in query.lower():
                function_calls.append(FunctionCall(
                    function_name="aggregate_data",
                    parameters={"data": "$execute_query", "operation": "sum", "field": "amount"},
                    confidence=0.9,
                    reasoning="Calculate total amount"
                ))
            
            if "email" in query.lower() or "send" in query.lower():
                function_calls.append(FunctionCall(
                    function_name="send_email",
                    parameters={"to": "<EMAIL>", "subject": "Summary Report", "body": "$aggregate_data"},
                    confidence=0.7,
                    reasoning="Send summary via email"
                ))
            
            execution_plan = ExecutionPlan(
                function_calls=function_calls,
                dependencies={"aggregate_data": ["execute_query"], "send_email": ["aggregate_data"]},
                reasoning="Demo execution plan based on simple heuristics",
                confidence=0.7
            )
            
            return PipelineResult(
                query=query,
                execution_plan=execution_plan,
                success=True
            )
        
        def get_available_functions(self):
            return [func.name for func in self.function_registry.get_all_functions()]
        
        def search_functions(self, term):
            results = self.function_registry.search_functions(term)
            return [func.name for func in results]
    
    return DemoPipeline()


if __name__ == "__main__":
    main()
