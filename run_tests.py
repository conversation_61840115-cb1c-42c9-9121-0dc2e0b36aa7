#!/usr/bin/env python3
"""
Fixed Test Runner - Self-contained tests that work without complex imports.
"""

import sys
import os
from pathlib import Path

def test_simple_pipeline():
    """Test the simple pipeline implementation."""
    print("🧪 Testing Simple Pipeline...")
    
    try:
        # Import the working simple demo
        from simple_demo import SimplePipeline, FunctionCall, ExecutionPlan, PipelineResult
        
        # Create pipeline
        pipeline = SimplePipeline()
        print(f"✅ Pipeline created with {len(pipeline.get_available_functions())} functions")
        
        # Test function availability
        functions = pipeline.get_available_functions()
        assert len(functions) > 0, "Should have functions"
        
        # Test function search
        email_functions = pipeline.search_functions("email")
        assert len(email_functions) > 0, "Should find email functions"
        
        data_functions = pipeline.search_functions("data")
        assert len(data_functions) > 0, "Should find data functions"
        
        # Test query processing
        query = "Get customer data and send email report"
        result = pipeline.process_query(query)
        
        assert result.success, "Query processing should succeed"
        assert len(result.execution_plan.function_calls) > 0, "Should have function calls"
        assert result.execution_plan.confidence > 0, "Should have confidence score"
        
        print("✅ Simple Pipeline tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Simple Pipeline tests failed: {e}")
        return False


def test_function_categories():
    """Test function categorization."""
    print("🧪 Testing Function Categories...")
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        functions = pipeline.get_available_functions()
        
        # Test that we have functions from different categories
        categories = set()
        for func_name in functions:
            func_info = pipeline.functions[func_name]
            categories.add(func_info["category"])
        
        expected_categories = {
            "data_processing", "file_operations", "communication", 
            "database", "web_scraping", "analytics"
        }
        
        found_categories = categories.intersection(expected_categories)
        assert len(found_categories) >= 4, f"Should have at least 4 categories, found: {found_categories}"
        
        print(f"✅ Function Categories test passed! Found {len(categories)} categories")
        return True
        
    except Exception as e:
        print(f"❌ Function Categories test failed: {e}")
        return False


def test_query_processing():
    """Test various query processing scenarios."""
    print("🧪 Testing Query Processing...")
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        # Test different types of queries
        test_queries = [
            "Get all users and send them an email",
            "Backup the database and notify admin",
            "Analyze sales data and create a report",
            "Filter customer data by status",
            "Scrape competitor prices and update database"
        ]
        
        successful_queries = 0
        
        for query in test_queries:
            result = pipeline.process_query(query)
            if result.success and len(result.execution_plan.function_calls) > 0:
                successful_queries += 1
        
        success_rate = successful_queries / len(test_queries)
        assert success_rate >= 0.8, f"Success rate too low: {success_rate:.2f}"
        
        print(f"✅ Query Processing test passed! {successful_queries}/{len(test_queries)} queries successful")
        return True
        
    except Exception as e:
        print(f"❌ Query Processing test failed: {e}")
        return False


def test_execution_plan_structure():
    """Test execution plan structure."""
    print("🧪 Testing Execution Plan Structure...")
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        # Test a complex query
        query = "Retrieve invoices, calculate totals, generate report, and send email"
        result = pipeline.process_query(query)
        
        assert result.success, "Query should succeed"
        
        plan = result.execution_plan
        assert hasattr(plan, 'function_calls'), "Should have function_calls"
        assert hasattr(plan, 'dependencies'), "Should have dependencies"
        assert hasattr(plan, 'reasoning'), "Should have reasoning"
        assert hasattr(plan, 'confidence'), "Should have confidence"
        
        assert isinstance(plan.function_calls, list), "function_calls should be a list"
        assert isinstance(plan.dependencies, dict), "dependencies should be a dict"
        assert isinstance(plan.reasoning, str), "reasoning should be a string"
        assert isinstance(plan.confidence, (int, float)), "confidence should be numeric"
        
        # Test function call structure
        if plan.function_calls:
            call = plan.function_calls[0]
            assert hasattr(call, 'function_name'), "Should have function_name"
            assert hasattr(call, 'parameters'), "Should have parameters"
            assert hasattr(call, 'confidence'), "Should have confidence"
            assert hasattr(call, 'reasoning'), "Should have reasoning"
        
        print("✅ Execution Plan Structure test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Execution Plan Structure test failed: {e}")
        return False


def test_function_search():
    """Test function search functionality."""
    print("🧪 Testing Function Search...")
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        # Test search terms
        search_tests = [
            ("email", 1),  # Should find at least 1 email function
            ("data", 5),   # Should find at least 5 data functions
            ("report", 1), # Should find at least 1 report function
            ("backup", 1), # Should find at least 1 backup function
        ]
        
        for term, min_expected in search_tests:
            results = pipeline.search_functions(term)
            assert len(results) >= min_expected, f"Search for '{term}' should find at least {min_expected} functions, found {len(results)}"
        
        # Test case insensitive search
        email_upper = pipeline.search_functions("EMAIL")
        email_lower = pipeline.search_functions("email")
        assert len(email_upper) == len(email_lower), "Search should be case insensitive"
        
        print("✅ Function Search test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Function Search test failed: {e}")
        return False


def test_dependency_handling():
    """Test dependency handling in execution plans."""
    print("🧪 Testing Dependency Handling...")
    
    try:
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        # Test a query that should create dependencies
        query = "Get data, process it, and send results via email"
        result = pipeline.process_query(query)
        
        assert result.success, "Query should succeed"
        
        plan = result.execution_plan
        
        # Check if dependencies make sense
        if plan.dependencies:
            for func, deps in plan.dependencies.items():
                # Each dependency should be a function that exists in the plan
                for dep in deps:
                    function_names = [call.function_name for call in plan.function_calls]
                    assert dep in function_names, f"Dependency '{dep}' should exist in function calls"
        
        print("✅ Dependency Handling test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Dependency Handling test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 AI Function Call Pipeline - Fixed Test Suite")
    print("=" * 55)
    
    tests = [
        test_simple_pipeline,
        test_function_categories,
        test_query_processing,
        test_execution_plan_structure,
        test_function_search,
        test_dependency_handling,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 55)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        
        # Interactive query processing
        print("\n✨ Ready to process your query!")
        try:
            user_query = input("Enter your query (or press Enter to skip): ").strip()
            
            if user_query:
                from simple_demo import SimplePipeline
                
                pipeline = SimplePipeline()
                result = pipeline.process_query(user_query)
                
                print(f"\n📋 Query Analysis:")
                print(f"Query: \"{user_query}\"")
                print(f"\nThe pipeline identified the need for:")

                for i, call in enumerate(result.execution_plan.function_calls, 1):
                    # Create more descriptive reasoning based on function name
                    if "execute_query" in call.function_name or "get" in call.function_name:
                        purpose = "retrieve data"
                    elif "aggregate" in call.function_name or "sum" in call.function_name or "calculate" in call.function_name:
                        purpose = "summarize/calculate data"
                    elif "send_email" in call.function_name or "email" in call.function_name:
                        purpose = "send email"
                    elif "filter" in call.function_name:
                        purpose = "filter data"
                    elif "generate_report" in call.function_name or "report" in call.function_name:
                        purpose = "generate report"
                    elif "scrape" in call.function_name:
                        purpose = "extract web data"
                    elif "backup" in call.function_name:
                        purpose = "backup data"
                    elif "create_visualization" in call.function_name or "chart" in call.function_name:
                        purpose = "create visualization"
                    else:
                        purpose = call.reasoning.lower()

                    print(f"  - A function to {purpose}: {call.function_name}")

                print(f"\n📋 Detailed Execution Plan:")
                print(f"Success: {result.success}")
                print(f"Confidence: {result.execution_plan.confidence:.2f}")
                print(f"Total Functions: {len(result.execution_plan.function_calls)}")

                for i, call in enumerate(result.execution_plan.function_calls, 1):
                    print(f"\n  Step {i}: {call.function_name}")
                    print(f"    Parameters: {call.parameters}")
                    print(f"    Purpose: {call.reasoning}")

                if result.execution_plan.dependencies:
                    print(f"\n🔗 Function Dependencies:")
                    for func, deps in result.execution_plan.dependencies.items():
                        if deps:
                            print(f"  {func} depends on: {', '.join(deps)}")

                print(f"\n💡 Overall Strategy: {result.execution_plan.reasoning}")
                print("\n🎉 Query processed successfully!")
            
        except KeyboardInterrupt:
            print("\n👋 Skipped user input")
        except Exception as e:
            print(f"\n❌ Error processing query: {e}")
    
    else:
        print(f"⚠️  {failed} tests failed")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
