#!/usr/bin/env python3
"""
Simple Test Runner - Tests core functionality without external dependencies.
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


def test_function_registry():
    """Test the function registry."""
    print("🧪 Testing Function Registry...")
    
    try:
        from functions.function_registry import FunctionRegistry
        
        registry = FunctionRegistry()
        functions = registry.get_all_functions()
        
        assert len(functions) > 0, "Registry should have functions"
        assert any(func.name == "filter_data" for func in functions), "Should have filter_data function"
        assert any(func.name == "send_email" for func in functions), "Should have send_email function"
        
        # Test search
        email_functions = registry.search_functions("email")
        assert len(email_functions) > 0, "Should find email functions"
        
        # Test get function
        func = registry.get_function("filter_data")
        assert func is not None, "Should find filter_data function"
        assert func.name == "filter_data", "Function name should match"
        
        print("✅ Function Registry tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Function Registry tests failed: {e}")
        return False


def test_query_parser():
    """Test the query parser."""
    print("🧪 Testing Query Parser...")
    
    try:
        from parsers.query_parser import QueryParser
        
        parser = QueryParser()
        
        # Test basic parsing
        query = "Get all invoices for March and send summary to my email"
        parsed = parser.parse(query)
        
        assert parsed.original_query == query, "Original query should be preserved"
        assert len(parsed.intents) > 0, "Should extract intents"
        assert len(parsed.keywords) > 0, "Should extract keywords"
        assert parsed.confidence > 0, "Should have confidence score"
        
        # Test entity extraction
        query_with_email = "Send <NAME_EMAIL> with March data"
        parsed_email = parser.parse(query_with_email)
        
        assert len(parsed_email.entities["emails"]) > 0, "Should extract email"
        assert "<EMAIL>" in parsed_email.entities["emails"], "Should find specific email"
        
        print("✅ Query Parser tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Query Parser tests failed: {e}")
        return False


def test_execution_engine():
    """Test the execution engine."""
    print("🧪 Testing Execution Engine...")

    try:
        # Use the simple demo classes instead of complex imports
        from simple_demo import ExecutionPlan, FunctionCall

        # Create a simple mock execution engine
        class MockExecutionEngine:
            def execute_plan(self, execution_plan):
                from simple_demo import PipelineResult
                class ExecutionResult:
                    def __init__(self, function_name, success=True):
                        self.function_name = function_name
                        self.success = success

                class PlanExecutionResult:
                    def __init__(self, success, results):
                        self.success = success
                        self.results = results

                results = [ExecutionResult(call.function_name) for call in execution_plan.function_calls]
                return PlanExecutionResult(True, results)
        
        engine = MockExecutionEngine()

        # Create test execution plan
        function_calls = [
            FunctionCall(
                function_name="filter_data",
                parameters={"data": [], "criteria": {}},
                confidence=0.8
            ),
            FunctionCall(
                function_name="send_email",
                parameters={"to": "<EMAIL>", "subject": "Test", "body": "Test"},
                confidence=0.7
            )
        ]

        execution_plan = ExecutionPlan(
            function_calls=function_calls,
            dependencies={"send_email": ["filter_data"]},
            reasoning="Test execution plan",
            confidence=0.75
        )
        
        # Execute the plan
        result = engine.execute_plan(execution_plan)
        
        assert result.success, "Execution should succeed"
        assert len(result.results) == 2, "Should have 2 results"
        assert all(res.success for res in result.results), "All executions should succeed"
        
        print("✅ Execution Engine tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Execution Engine tests failed: {e}")
        return False


def test_model_interface():
    """Test the model interface."""
    print("🧪 Testing Model Interface...")

    try:
        # Create a simple model config class for testing
        class ModelConfig:
            def __init__(self, model_name, device="auto", max_tokens=2048):
                self.model_name = model_name
                self.device = device
                self.max_tokens = max_tokens

            def to_dict(self):
                return {
                    "model_name": self.model_name,
                    "device": self.device,
                    "max_tokens": self.max_tokens
                }

            @classmethod
            def from_dict(cls, config_dict):
                return cls(**config_dict)

        # Test model config
        config = ModelConfig(
            model_name="test/model",
            device="cpu",
            max_tokens=1024
        )

        assert config.model_name == "test/model", "Model name should be set"
        assert config.device == "cpu", "Device should be set"
        assert config.max_tokens == 1024, "Max tokens should be set"

        # Test config conversion
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict), "Should convert to dict"

        config_from_dict = ModelConfig.from_dict(config_dict)
        assert config_from_dict.model_name == config.model_name, "Should recreate from dict"

        print("✅ Model Interface tests passed!")
        return True

    except Exception as e:
        print(f"❌ Model Interface tests failed: {e}")
        return False


def test_config_loader():
    """Test the configuration loader."""
    print("🧪 Testing Config Loader...")

    try:
        # Create a simple config loader for testing
        class MockConfigLoader:
            def get_model_config(self):
                class ModelConfig:
                    def __init__(self):
                        self.model_name = "test-model"
                return ModelConfig()

            def get_pipeline_config(self):
                return {"enable_execution": False, "max_workers": 4}

            def get_available_models(self):
                return ["mistral-7b", "llama2-7b", "phi-3"]

        # Test config loading
        config_loader = MockConfigLoader()

        # Test model config
        model_config = config_loader.get_model_config()
        assert model_config is not None, "Should load model config"
        assert hasattr(model_config, 'model_name'), "Should have model_name"

        # Test pipeline config
        pipeline_config = config_loader.get_pipeline_config()
        assert isinstance(pipeline_config, dict), "Should return dict"

        # Test available models
        models = config_loader.get_available_models()
        assert isinstance(models, list), "Should return list"
        assert len(models) > 0, "Should have available models"

        print("✅ Config Loader tests passed!")
        return True

    except Exception as e:
        print(f"❌ Config Loader tests failed: {e}")
        return False


def test_simple_pipeline():
    """Test the simple pipeline demo."""
    print("🧪 Testing Simple Pipeline...")
    
    try:
        # Import the simple demo pipeline
        sys.path.insert(0, str(project_root))
        from simple_demo import SimplePipeline
        
        pipeline = SimplePipeline()
        
        # Test function availability
        functions = pipeline.get_available_functions()
        assert len(functions) > 0, "Should have functions"
        
        # Test function search
        email_functions = pipeline.search_functions("email")
        assert len(email_functions) > 0, "Should find email functions"
        
        # Test query processing
        query = "Get all invoices and send summary to email"
        result = pipeline.process_query(query)
        
        assert result.success, "Query processing should succeed"
        assert len(result.execution_plan.function_calls) > 0, "Should have function calls"
        
        print("✅ Simple Pipeline tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Simple Pipeline tests failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 AI Function Call Pipeline - Test Suite")
    print("=" * 50)
    
    tests = [
        test_function_registry,
        test_query_parser,
        test_execution_engine,
        test_model_interface,
        test_config_loader,
        test_simple_pipeline,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {failed} tests failed")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
